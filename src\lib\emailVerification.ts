import { supabase } from './supabase';

interface UserData {
  first_name: string;
  last_name: string;
  phone: string;
  age: number;
  gender: 'male' | 'female';
  city: string;
  education?: string;
  profession?: string;
  religious_commitment?: string;
  bio?: string;
}

interface EmailVerification {
  id: string;
  email: string;
  verification_token: string;
  user_data: UserData;
  status: 'pending' | 'verified' | 'expired';
  expires_at: string;
  created_at: string;
  verified_at?: string;
}

interface VerificationResult {
  success: boolean;
  verification?: EmailVerification;
  error?: string;
}

class EmailVerificationService {
  // إنشاء طلب تحقق جديد
  async createVerification(email: string, userData: UserData): Promise<{ success: boolean; token?: string; error?: string }> {
    try {
      // التحقق من وجود طلب تحقق معلق
      const { data: existingVerifications } = await supabase
        .from('email_verifications')
        .select('id, verification_token, expires_at')
        .eq('email', email)
        .eq('status', 'pending')
        .gt('expires_at', new Date().toISOString());

      if (existingVerifications && existingVerifications.length > 0) {
        return { 
          success: false, 
          error: 'يوجد طلب تحقق معلق بالفعل لهذا البريد الإلكتروني' 
        };
      }

      // توليد رمز تحقق فريد
      const token = await this.generateToken();
      
      // تاريخ انتهاء الصلاحية (24 ساعة من الآن)
      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + 24);

      // إنشاء طلب التحقق
      const { data, error } = await supabase
        .from('email_verifications')
        .insert({
          email,
          verification_token: token,
          user_data: userData,
          status: 'pending',
          expires_at: expiresAt.toISOString()
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating verification:', error);
        return { success: false, error: 'حدث خطأ في إنشاء طلب التحقق' };
      }

      return { success: true, token };
    } catch (error) {
      console.error('Error in createVerification:', error);
      return { success: false, error: 'حدث خطأ غير متوقع' };
    }
  }

  // التحقق من صحة الرمز
  async verifyToken(token: string): Promise<VerificationResult> {
    try {
      const { data, error } = await supabase
        .from('email_verifications')
        .select('*')
        .eq('verification_token', token)
        .eq('status', 'pending')
        .gt('expires_at', new Date().toISOString())
        .single();

      if (error || !data) {
        return { 
          success: false, 
          error: 'رمز التحقق غير صحيح أو منتهي الصلاحية' 
        };
      }

      return { success: true, verification: data };
    } catch (error) {
      console.error('Error in verifyToken:', error);
      return { success: false, error: 'حدث خطأ في التحقق من الرمز' };
    }
  }

  // توليد رمز تحقق فريد
  private async generateToken(): Promise<string> {
    const array = new Uint8Array(32);
    crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  }

  // توليد رقم عضوية فريد
  async generateMembershipNumber(): Promise<string> {
    let membershipNumber: string;
    let isUnique = false;
    
    while (!isUnique) {
      // توليد رقم عضوية من 6 أرقام
      membershipNumber = Math.floor(100000 + Math.random() * 900000).toString();
      
      // التحقق من عدم وجود هذا الرقم مسبقاً
      const { data } = await supabase
        .from('users')
        .select('id')
        .eq('membership_number', membershipNumber)
        .limit(1);
      
      if (!data || data.length === 0) {
        isUnique = true;
      }
    }
    
    return membershipNumber!;
  }

  // إرسال بريد التحقق
  async sendVerificationEmail(email: string, token: string): Promise<{ success: boolean; error?: string }> {
    try {
      const verificationUrl = `${window.location.origin}/verify?token=${token}`;
      
      const { error } = await supabase.functions.invoke('send-verification-email', {
        body: {
          email,
          verificationUrl
        }
      });

      if (error) {
        console.error('Error sending verification email:', error);
        return { success: false, error: 'حدث خطأ في إرسال بريد التحقق' };
      }

      return { success: true };
    } catch (error) {
      console.error('Error in sendVerificationEmail:', error);
      return { success: false, error: 'حدث خطأ غير متوقع' };
    }
  }

  // تشفير كلمة المرور
  private async hashPassword(password: string): Promise<string> {
    const encoder = new TextEncoder();
    const data = encoder.encode(password);
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  }

  // تأكيد التحقق وإنشاء المستخدم - نسخة مبسطة
  async confirmVerification(
    token: string,
    password: string
  ): Promise<{ success: boolean; user?: any; error?: string }> {
    try {
      // التحقق من صحة الرمز
      const verificationResult = await this.verifyToken(token);
      if (!verificationResult.success || !verificationResult.verification) {
        return { success: false, error: verificationResult.error };
      }

      const verification = verificationResult.verification;

      // محاولة إنشاء مستخدم جديد مع تأكيد البريد الإلكتروني
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email: verification.email,
        password: password,
        options: {
          emailRedirectTo: undefined, // لا نريد إرسال بريد تأكيد إضافي
        }
      });

      let authUser = authData?.user;

      // إذا كان المستخدم موجود بالفعل، نحاول تسجيل الدخول
      if (authError && authError.message.includes('already registered')) {
        console.log('User already exists in auth, attempting sign in...');

        const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
          email: verification.email,
          password: password,
        });

        if (signInError) {
          console.error('Sign in error:', signInError);
          return { success: false, error: 'حدث خطأ في تسجيل الدخول' };
        }

        authUser = signInData.user;
      } else if (authError) {
        console.error('Auth error:', authError);
        return { success: false, error: 'حدث خطأ في إنشاء الحساب: ' + authError.message };
      }

      if (!authUser) {
        return { success: false, error: 'حدث خطأ في إنشاء الحساب' };
      }

      // تأكيد البريد الإلكتروني يدوياً إذا لم يكن مؤكداً
      if (!authUser.email_confirmed_at) {
        console.log('Confirming email for user:', authUser.id);

        try {
          // محاولة استخدام دالة قاعدة البيانات لتأكيد البريد الإلكتروني
          const { error: confirmError } = await supabase.rpc('confirm_user_email', {
            user_id: authUser.id
          });

          if (confirmError) {
            console.error('Error confirming email via RPC:', confirmError);

            // طريقة بديلة: تسجيل الدخول مرة أخرى لتحديث حالة المستخدم
            console.log('Attempting alternative email confirmation...');
            const { data: refreshData } = await supabase.auth.refreshSession();
            if (refreshData?.user) {
              authUser = refreshData.user;
              console.log('✅ User session refreshed');
            }
          } else {
            console.log('✅ Email confirmed successfully via RPC');

            // تحديث بيانات المستخدم المحلية
            authUser = { ...authUser, email_confirmed_at: new Date().toISOString() };
          }
        } catch (error) {
          console.error('Error in email confirmation process:', error);
          // نستمر حتى لو فشل التأكيد، لأن المستخدم تم إنشاؤه بنجاح
        }
      }

      // التحقق من وجود ملف شخصي في جدول users
      const { data: existingProfile } = await supabase
        .from('users')
        .select('id, first_name, last_name, membership_number')
        .eq('id', authUser.id)
        .single();

      if (existingProfile) {
        // الملف الشخصي موجود، نحتاج لتحديثه بالبيانات الجديدة
        console.log('Updating existing user profile with new data');

        // توليد رقم عضوية إذا لم يكن موجوداً
        let membershipNumber = existingProfile.membership_number;
        if (!membershipNumber) {
          membershipNumber = await emailVerificationService.generateMembershipNumber();
          console.log(`🎫 Generated membership number: ${membershipNumber}`);
        }

        const updateData = {
          first_name: verification.user_data.first_name,
          last_name: verification.user_data.last_name,
          phone: verification.user_data.phone,
          age: verification.user_data.age,
          gender: verification.user_data.gender,
          city: verification.user_data.city,
          membership_number: membershipNumber,
          education: verification.user_data.education || null,
          profession: verification.user_data.profession || null,
          job_title: verification.user_data.profession || null, // نقل المهنة إلى job_title أيضاً
          religious_commitment: verification.user_data.religious_commitment || null,
          bio: verification.user_data.bio || null,
          // إضافة البيانات المتقدمة المفقودة
          nationality: verification.user_data.nationality || null,
          weight: verification.user_data.weight || null,
          height: verification.user_data.height || null,
          religiosity_level: verification.user_data.religiosity_level || null,
          prayer_commitment: verification.user_data.prayer_commitment || null,
          smoking: verification.user_data.smoking || null,
          beard: verification.user_data.beard || null,
          hijab: verification.user_data.hijab || null,
          education_level: verification.user_data.education_level || null,
          financial_status: verification.user_data.financial_status || null,
          verified: true,
          updated_at: new Date().toISOString()
        };

        const { error: updateError } = await supabase
          .from('users')
          .update(updateData)
          .eq('id', authUser.id);

        if (updateError) {
          console.error('Error updating user profile:', updateError);
          return { success: false, error: 'حدث خطأ في تحديث الملف الشخصي' };
        }

        console.log('✅ User profile updated successfully');
      } else {
        // الملف الشخصي غير موجود، ننشئه
        console.log('Creating new user profile for auth user');

        // توليد رقم عضوية فريد
        const membershipNumber = await emailVerificationService.generateMembershipNumber();
        console.log(`🎫 Generated membership number: ${membershipNumber}`);

        // إنشاء الملف الشخصي مباشرة مع جميع البيانات
        const profileData = {
          id: authUser.id,
          email: verification.email,
          first_name: verification.user_data.first_name,
          last_name: verification.user_data.last_name,
          phone: verification.user_data.phone,
          age: verification.user_data.age,
          gender: verification.user_data.gender,
          city: verification.user_data.city,
          membership_number: membershipNumber,
          education: verification.user_data.education || null,
          profession: verification.user_data.profession || null,
          job_title: verification.user_data.profession || null, // نقل المهنة إلى job_title أيضاً
          religious_commitment: verification.user_data.religious_commitment || null,
          bio: verification.user_data.bio || null,
          // إضافة البيانات المتقدمة
          nationality: verification.user_data.nationality || null,
          weight: verification.user_data.weight || null,
          height: verification.user_data.height || null,
          religiosity_level: verification.user_data.religiosity_level || null,
          prayer_commitment: verification.user_data.prayer_commitment || null,
          smoking: verification.user_data.smoking || null,
          beard: verification.user_data.beard || null,
          hijab: verification.user_data.hijab || null,
          education_level: verification.user_data.education_level || null,
          financial_status: verification.user_data.financial_status || null,
          verified: true,
          status: 'active',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };

        const { error: profileError } = await supabase
          .from('users')
          .insert(profileData);

        if (profileError) {
          console.error('Profile creation error:', profileError);
          return { success: false, error: 'حدث خطأ في إنشاء الملف الشخصي' };
        }

        console.log('✅ User profile created successfully');
      }

      // تحديث حالة التحقق
      await supabase
        .from('email_verifications')
        .update({
          status: 'verified',
          verified_at: new Date().toISOString()
        })
        .eq('id', verification.id);

      return { success: true, user: authUser };

    } catch (error) {
      console.error('Error in confirmVerification:', error);
      return { success: false, error: 'حدث خطأ غير متوقع' };
    }
  }

  // تنظيف الطلبات المنتهية الصلاحية
  async cleanupExpiredVerifications(): Promise<void> {
    try {
      const { error } = await supabase
        .from('email_verifications')
        .delete()
        .lt('expires_at', new Date().toISOString());

      if (error) {
        console.error('Error cleaning up expired verifications:', error);
      }
    } catch (error) {
      console.error('Error in cleanupExpiredVerifications:', error);
    }
  }

  // حذف طلب تحقق
  async deleteVerification(token: string): Promise<{ success: boolean; error?: string }> {
    try {
      const { error } = await supabase
        .from('email_verifications')
        .delete()
        .eq('verification_token', token);

      if (error) {
        console.error('Error deleting verification:', error);
        return { success: false, error: 'حدث خطأ في حذف طلب التحقق' };
      }

      return { success: true };
    } catch (error) {
      console.error('Error in deleteVerification:', error);
      return { success: false, error: 'حدث خطأ غير متوقع' };
    }
  }

  // الحصول على طلب تحقق بواسطة البريد الإلكتروني
  async getVerificationByEmail(email: string): Promise<{ success: boolean; verification?: EmailVerification; error?: string }> {
    try {
      const { data, error } = await supabase
        .from('email_verifications')
        .select('*')
        .eq('email', email)
        .eq('status', 'pending')
        .gt('expires_at', new Date().toISOString())
        .single();

      if (error || !data) {
        return { success: false, error: 'لا يوجد طلب تحقق معلق لهذا البريد الإلكتروني' };
      }

      return { success: true, verification: data };
    } catch (error) {
      console.error('Error in getVerificationByEmail:', error);
      return { success: false, error: 'حدث خطأ غير متوقع' };
    }
  }

  // تحديث بيانات المستخدم في طلب التحقق
  async updateVerificationData(token: string, userData: UserData): Promise<{ success: boolean; error?: string }> {
    try {
      const { error } = await supabase
        .from('email_verifications')
        .update({ user_data: userData })
        .eq('verification_token', token)
        .eq('status', 'pending');

      if (error) {
        console.error('Error updating verification data:', error);
        return { success: false, error: 'حدث خطأ في تحديث بيانات التحقق' };
      }

      return { success: true };
    } catch (error) {
      console.error('Error in updateVerificationData:', error);
      return { success: false, error: 'حدث خطأ غير متوقع' };
    }
  }

  // إعادة إرسال بريد التحقق
  async resendVerificationEmail(email: string): Promise<{ success: boolean; error?: string }> {
    try {
      const verificationResult = await this.getVerificationByEmail(email);
      if (!verificationResult.success || !verificationResult.verification) {
        return { success: false, error: verificationResult.error };
      }

      const verification = verificationResult.verification;
      return await this.sendVerificationEmail(email, verification.verification_token);
    } catch (error) {
      console.error('Error in resendVerificationEmail:', error);
      return { success: false, error: 'حدث خطأ غير متوقع' };
    }
  }

  // التحقق من وجود مستخدم بالبريد الإلكتروني
  async checkUserExists(email: string): Promise<{ exists: boolean; inAuth: boolean; inDatabase: boolean }> {
    try {
      // التحقق من وجود المستخدم في جدول users
      const { data: dbUser } = await supabase
        .from('users')
        .select('id')
        .eq('email', email)
        .single();

      // التحقق من وجود المستخدم في auth.users عن طريق محاولة إعادة تعيين كلمة المرور
      const { error: authError } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/reset-password`
      });

      // إذا لم يكن هناك خطأ، فالمستخدم موجود في auth
      const inAuth = !authError || !authError.message.includes('not found');
      const inDatabase = !!dbUser;

      return {
        exists: inAuth || inDatabase,
        inAuth,
        inDatabase
      };
    } catch (error) {
      console.error('Error in checkUserExists:', error);
      return { exists: false, inAuth: false, inDatabase: false };
    }
  }
}

// إنشاء مثيل واحد من الخدمة
export const emailVerificationService = new EmailVerificationService();

// تصدير الأنواع
export type { UserData, EmailVerification, VerificationResult };
